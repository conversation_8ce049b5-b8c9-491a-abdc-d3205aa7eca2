import 'package:json_annotation/json_annotation.dart';

part 'filter_model.g.dart';

@JsonSerializable()
class FilterResponse {
  @Json<PERSON>ey(name: 'status')
  final int status;

  @Json<PERSON>ey(name: 'message')
  final String message;

  @Json<PERSON>ey(name: 'categories')
  final List<FilterCategory> categories;

  @Json<PERSON>ey(name: 'languages')
  final List<FilterLanguage> languages;

  @JsonKey(name: 'genres')
  final List<FilterGenre> genres;

  FilterResponse({
    required this.status,
    required this.message,
    required this.categories,
    required this.languages,
    required this.genres,
  });

  factory FilterResponse.fromJson(Map<String, dynamic> json) =>
      _$FilterResponseFromJson(json);

  Map<String, dynamic> toJson() => _$FilterResponseToJson(this);

  bool get success => status == 1;
}

@JsonSerializable()
class FilterCategory {
  @JsonKey(name: 'cat_id')
  final int catId;

  @<PERSON>son<PERSON>ey(name: 'cat_name')
  final String catName;

  @Json<PERSON>ey(name: 'cat_img')
  final String? catImg;

  FilterCategory({
    required this.catId,
    required this.catName,
    this.catImg,
  });

  factory FilterCategory.fromJson(Map<String, dynamic> json) =>
      _$FilterCategoryFromJson(json);

  Map<String, dynamic> toJson() => _$FilterCategoryToJson(this);

  @override
  String toString() {
    return 'FilterCategory(catId: $catId, catName: $catName)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is FilterCategory && other.catId == catId;
  }

  @override
  int get hashCode => catId.hashCode;
}

@JsonSerializable()
class FilterLanguage {
  @JsonKey(name: 'lang_id')
  final int langId;

  @JsonKey(name: 'language')
  final String language;

  FilterLanguage({
    required this.langId,
    required this.language,
  });

  factory FilterLanguage.fromJson(Map<String, dynamic> json) =>
      _$FilterLanguageFromJson(json);

  Map<String, dynamic> toJson() => _$FilterLanguageToJson(this);

  @override
  String toString() {
    return 'FilterLanguage(langId: $langId, language: $language)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is FilterLanguage && other.langId == langId;
  }

  @override
  int get hashCode => langId.hashCode;
}

@JsonSerializable()
class FilterGenre {
  @JsonKey(name: 'genre_id')
  final int genreId;

  @JsonKey(name: 'genre')
  final String genre;

  FilterGenre({
    required this.genreId,
    required this.genre,
  });

  factory FilterGenre.fromJson(Map<String, dynamic> json) =>
      _$FilterGenreFromJson(json);

  Map<String, dynamic> toJson() => _$FilterGenreToJson(this);

  @override
  String toString() {
    return 'FilterGenre(genreId: $genreId, genre: $genre)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is FilterGenre && other.genreId == genreId;
  }

  @override
  int get hashCode => genreId.hashCode;
}

// Model for selected filters
class SelectedFilters {
  final List<FilterCategory> selectedCategories;
  final List<FilterLanguage> selectedLanguages;
  final List<FilterGenre> selectedGenres;

  SelectedFilters({
    this.selectedCategories = const [],
    this.selectedLanguages = const [],
    this.selectedGenres = const [],
  });

  bool get hasAnyFilter =>
      selectedCategories.isNotEmpty ||
      selectedLanguages.isNotEmpty ||
      selectedGenres.isNotEmpty;

  SelectedFilters copyWith({
    List<FilterCategory>? selectedCategories,
    List<FilterLanguage>? selectedLanguages,
    List<FilterGenre>? selectedGenres,
  }) {
    return SelectedFilters(
      selectedCategories: selectedCategories ?? this.selectedCategories,
      selectedLanguages: selectedLanguages ?? this.selectedLanguages,
      selectedGenres: selectedGenres ?? this.selectedGenres,
    );
  }

  @override
  String toString() {
    return 'SelectedFilters(categories: ${selectedCategories.length}, languages: ${selectedLanguages.length}, genres: ${selectedGenres.length})';
  }
}
