import 'package:my_video/app_imports.dart';
import 'widgets/filter_bottom_sheet.dart';

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => HomePageState();
}

class HomePageState extends State<HomePage> with AutomaticKeepAliveClientMixin {
  HomePageHelper? _homePageHelper;
  late HomeController homeController;
  late PageController _pageController;
  int _currentPage = 0;
  Timer? _autoScrollTimer;

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    _pageController = PageController(viewportFraction: 0.85);
    // Initialize helper only once
    _homePageHelper = HomePageHelper(this);
    _startAutoScroll();
  }

  @override
  void dispose() {
    _autoScrollTimer?.cancel();
    _pageController.dispose();
    super.dispose();
  }

  void _startAutoScroll() {
    _autoScrollTimer = Timer.periodic(const Duration(seconds: 4), (timer) {
      if (_homePageHelper?.featuredMovies.isNotEmpty == true) {
        final nextPage =
            (_currentPage + 1) % _homePageHelper!.featuredMovies.length;
        _pageController.animateToPage(
          nextPage,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      }
    });
  }

  void _stopAutoScroll() {
    _autoScrollTimer?.cancel();
  }

  void _restartAutoScroll() {
    _stopAutoScroll();
    _startAutoScroll();
  }

  void _showFilterBottomSheet() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => FilterBottomSheet(
        onFiltersApplied: (selectedFilters) {
          // For now, just show a toast with selected filters
          AppHelper.showToast('Filters applied: ${selectedFilters.toString()}');
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // Required for AutomaticKeepAliveClientMixin
    return GetBuilder<HomeController>(
      init: Get.put(HomeController(), permanent: true),
      builder: (HomeController controller) {
        homeController = controller;
        return Scaffold(extendBody: true, body: _bodyView());
      },
    );
  }

  Widget _appBar() {
    return Padding(
      padding: const EdgeInsets.all(8.0).copyWith(left: 16),
      child: Row(
        children: [
          const AppText(
            text: 'My Video',
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: AppColorConstants.textPrimary,
          ),
          const Spacer(),
          IconButton(
            icon: const Icon(
              Icons.search,
              color: AppColorConstants.textPrimary,
            ),
            onPressed: () {},
          ),
          IconButton(
            icon: const Icon(
              Icons.filter_list,
              color: AppColorConstants.textPrimary,
            ),
            onPressed: () => _showFilterBottomSheet(),
          ),
        ],
      ),
    );
  }

  Widget _bodyView() {
    return _homePageHelper!.isLoading
        ? const Center(child: CircularProgressIndicator())
        : SafeArea(
            bottom: false,
            child: RefreshIndicator(
              onRefresh: _homePageHelper!.refreshData,
              child: SingleChildScrollView(
                physics: const AlwaysScrollableScrollPhysics(),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _appBar(),
                    if (_homePageHelper!.featuredMovies.isNotEmpty) ...[
                      _buildFeaturedBanner(),
                      Space.height(24),
                    ],

                    ..._homePageHelper!.categories.map((category) {
                      final movies = _homePageHelper!.getMoviesByCategory(
                        category.name,
                      );
                      if (movies.isEmpty) return const SizedBox.shrink();

                      return _buildCategorySection(category, movies);
                    }),

                    Space.height(120), // Bottom padding for FAB
                  ],
                ),
              ),
            ),
          );
  }

  Widget _buildFeaturedBanner() {
    if (_homePageHelper!.featuredMovies.isEmpty) {
      return SizedBox(
        height: MySize.height(250),
        child: LoadingManager.buildLoadingWidget(
          message: 'Loading featured movies...',
        ),
      );
    }

    return Column(
      children: [
        GestureDetector(
          onPanDown: (_) => _stopAutoScroll(),
          onPanEnd: (_) => _restartAutoScroll(),
          child: SizedBox(
            height: MySize.height(220),
            child: PageView.builder(
              itemCount: _homePageHelper!.featuredMovies.length,
              controller: _pageController,
              onPageChanged: (index) {
                setState(() {
                  _currentPage = index;
                });
              },
              itemBuilder: (context, index) {
                final movie = _homePageHelper!.featuredMovies[index];
                return BannerMovieCard(
                  movie: movie,
                  onTap: () => _homePageHelper!.playMovie(movie, context),
                );
              },
            ),
          ),
        ),
        Space.height(12),
        _buildDotIndicators(),
      ],
    );
  }

  Widget _buildDotIndicators() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(
        _homePageHelper!.featuredMovies.length,
        (index) => Container(
          margin: EdgeInsets.symmetric(horizontal: MySize.width(4)),
          width: MySize.width(_currentPage == index ? 24 : 8),
          height: MySize.height(8),
          decoration: BoxDecoration(
            color: _currentPage == index
                ? AppColorConstants.primaryColor
                : AppColorConstants.dividerColor,
            borderRadius: BorderRadius.circular(MySize.radius(4)),
          ),
        ),
      ),
    );
  }

  Widget _buildCategorySection(
    CategoryWiseData category,
    List<MovieModel> movies,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.symmetric(horizontal: MySize.width(16)),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              AppText(
                text: category.name,
                fontSize: MySize.fontSize(18),
                fontWeight: FontWeight.bold,
                color: AppColorConstants.textPrimary,
              ),
              GestureDetector(
                onTap: () => _homePageHelper!.viewAllMovies(category),
                child: AppText(
                  text: 'See All',
                  fontSize: MySize.fontSize(14),
                  color: AppColorConstants.primaryColor,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
        Space.height(12),
        SizedBox(
          height: MySize.height(240),
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            padding: EdgeInsets.symmetric(horizontal: MySize.width(16)),
            itemCount: movies.length + 1, // +1 for single ad
            itemBuilder: (context, index) {
              final categoryIndex = _homePageHelper!.categories.indexOf(
                category,
              );
              final isEvenCategory = categoryIndex % 2 == 0;
              final adPosition = isEvenCategory ? 0 : movies.length;

              if (index == adPosition) {
                final bannerAd = AdsManager.getBannerAdWidget(
                  index: categoryIndex % 10,
                  uniqueId: 'category_${category.name}_ad_$categoryIndex',
                );
                return bannerAd ?? AdsManager.buildAdPlaceholder();
              }

              // Movie items
              final movieIndex = isEvenCategory ? index - 1 : index;
              if (movieIndex < 0 || movieIndex >= movies.length) {
                return const SizedBox.shrink();
              }

              final movie = movies[movieIndex];
              return MovieCard(
                movie: movie,
                onTap: () => _homePageHelper!.playMovie(movie, context),
                showTitle: true,
                showRating: true,
                showDuration: true,
              );
            },
          ),
        ),
        Space.height(24),
      ],
    );
  }
}
