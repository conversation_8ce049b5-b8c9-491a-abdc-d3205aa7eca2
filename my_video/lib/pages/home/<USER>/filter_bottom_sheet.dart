import 'package:my_video/app_imports.dart';

class FilterBottomSheet extends StatefulWidget {
  final Function(SelectedFilters) onFiltersApplied;

  const FilterBottomSheet({super.key, required this.onFiltersApplied});

  @override
  State<FilterBottomSheet> createState() => _FilterBottomSheetState();
}

class _FilterBottomSheetState extends State<FilterBottomSheet> {
  FilterResponse? _filterResponse;
  bool _isLoading = true;
  String? _errorMessage;

  // Selected filters
  final List<FilterCategory> _selectedCategories = [];
  final List<FilterLanguage> _selectedLanguages = [];
  final List<FilterGenre> _selectedGenres = [];

  @override
  void initState() {
    super.initState();
    _loadFilters();
  }

  Future<void> _loadFilters() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      final homeController = Get.find<HomeController>();
      final response = await homeController.getFiltersFromAPI();

      if (response.success) {
        setState(() {
          _filterResponse = response;
          _isLoading = false;
        });
      } else {
        setState(() {
          _errorMessage = response.message;
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Failed to load filters: $e';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.8,
      decoration: const BoxDecoration(
        color: AppColorConstants.backgroundColor,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Column(
        children: [
          _buildHeader(),
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _errorMessage != null
                ? _buildErrorView()
                : _buildFilterContent(),
          ),
          _buildActionButtons(),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: EdgeInsets.all(MySize.width(16)),
      decoration: const BoxDecoration(
        border: Border(
          bottom: BorderSide(color: AppColorConstants.dividerColor, width: 1),
        ),
      ),
      child: Row(
        children: [
          const AppText(
            text: 'Filters',
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: AppColorConstants.textPrimary,
          ),
          const Spacer(),
          IconButton(
            onPressed: () => Navigator.pop(context),
            icon: const Icon(Icons.close, color: AppColorConstants.textPrimary),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: MySize.height(64),
            color: AppColorConstants.colorRed,
          ),
          Space.height(16),
          AppText(
            text: _errorMessage ?? 'Something went wrong',
            fontSize: 16,
            color: AppColorConstants.textSecondary,
            textAlign: TextAlign.center,
          ),
          Space.height(16),
          AppButton(
            text: 'Retry',
            onPressed: _loadFilters,
            backgroundColor: AppColorConstants.primaryColor,
          ),
        ],
      ),
    );
  }

  Widget _buildFilterContent() {
    if (_filterResponse == null) return const SizedBox.shrink();

    return SingleChildScrollView(
      padding: EdgeInsets.all(MySize.width(16)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (_filterResponse!.categories.isNotEmpty) ...[
            _buildFilterSection(
              title: 'Categories',
              items: _filterResponse!.categories,
              selectedItems: _selectedCategories,
              onItemToggle: (category) => _toggleCategory(category),
              itemBuilder: (category) => category.catName,
            ),
            Space.height(24),
          ],
          if (_filterResponse!.languages.isNotEmpty) ...[
            _buildFilterSection(
              title: 'Languages',
              items: _filterResponse!.languages,
              selectedItems: _selectedLanguages,
              onItemToggle: (language) => _toggleLanguage(language),
              itemBuilder: (language) => language.language,
            ),
            Space.height(24),
          ],
          if (_filterResponse!.genres.isNotEmpty) ...[
            _buildFilterSection(
              title: 'Genres',
              items: _filterResponse!.genres,
              selectedItems: _selectedGenres,
              onItemToggle: (genre) => _toggleGenre(genre),
              itemBuilder: (genre) => genre.genre,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildFilterSection<T>({
    required String title,
    required List<T> items,
    required List<T> selectedItems,
    required Function(T) onItemToggle,
    required String Function(T) itemBuilder,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        AppText(
          text: title,
          fontSize: 18,
          fontWeight: FontWeight.bold,
          color: AppColorConstants.textPrimary,
        ),
        Space.height(12),
        Wrap(
          spacing: MySize.width(8),
          runSpacing: MySize.height(8),
          children: items.map((item) {
            final isSelected = selectedItems.contains(item);
            return FilterChip(
              label: AppText(
                text: itemBuilder(item),
                fontSize: 14,
                color: isSelected
                    ? AppColorConstants.textPrimary
                    : AppColorConstants.textSecondary,
              ),
              selected: isSelected,
              onSelected: (_) => onItemToggle(item),
              backgroundColor: AppColorConstants.cardColor,
              selectedColor: AppColorConstants.primaryColor,
              checkmarkColor: AppColorConstants.textPrimary,
              side: BorderSide(
                color: isSelected
                    ? AppColorConstants.primaryColor
                    : AppColorConstants.dividerColor,
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildActionButtons() {
    final hasSelectedFilters =
        _selectedCategories.isNotEmpty ||
        _selectedLanguages.isNotEmpty ||
        _selectedGenres.isNotEmpty;

    return Container(
      padding: EdgeInsets.all(MySize.width(16)),
      decoration: const BoxDecoration(
        border: Border(
          top: BorderSide(color: AppColorConstants.dividerColor, width: 1),
        ),
      ),
      child: Row(
        children: [
          if (hasSelectedFilters)
            Expanded(
              child: AppButton(
                text: 'Clear All',
                onPressed: _clearAllFilters,
                backgroundColor: AppColorConstants.cardColor,
                textColor: AppColorConstants.textSecondary,
              ),
            ),
          if (hasSelectedFilters) Space.width(12),
          Expanded(
            child: AppButton(
              text: 'Apply Filters',
              onPressed: _applyFilters,
              backgroundColor: AppColorConstants.primaryColor,
            ),
          ),
        ],
      ),
    );
  }

  void _toggleCategory(FilterCategory category) {
    setState(() {
      if (_selectedCategories.contains(category)) {
        _selectedCategories.remove(category);
      } else {
        _selectedCategories.add(category);
      }
    });
  }

  void _toggleLanguage(FilterLanguage language) {
    setState(() {
      if (_selectedLanguages.contains(language)) {
        _selectedLanguages.remove(language);
      } else {
        _selectedLanguages.add(language);
      }
    });
  }

  void _toggleGenre(FilterGenre genre) {
    setState(() {
      if (_selectedGenres.contains(genre)) {
        _selectedGenres.remove(genre);
      } else {
        _selectedGenres.add(genre);
      }
    });
  }

  void _clearAllFilters() {
    setState(() {
      _selectedCategories.clear();
      _selectedLanguages.clear();
      _selectedGenres.clear();
    });
  }

  void _applyFilters() {
    final selectedFilters = SelectedFilters(
      selectedCategories: _selectedCategories,
      selectedLanguages: _selectedLanguages,
      selectedGenres: _selectedGenres,
    );

    widget.onFiltersApplied(selectedFilters);
    Navigator.pop(context);
  }
}
