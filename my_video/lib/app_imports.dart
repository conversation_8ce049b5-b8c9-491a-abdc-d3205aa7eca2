// :::::::::::::::::::::::::::::::::::::::::::::: dart imports ::::::::::::::::::::::::::::::::::::::::::://
export 'dart:async';
export 'dart:convert';
export 'dart:io' hide Link;
export 'dart:ui'
    hide
        Image,
        decodeImageFromList,
        TextStyle,
        ImageDecoderCallback,
        Gradient,
        StrutStyle,
        Codec;

// :::::::::::::::::::::::::::::::::::::::::::::: package imports ::::::::::::::::::::::::::::::::::::::::::://
export 'package:bot_toast/bot_toast.dart';
export 'package:cached_network_image/cached_network_image.dart';
export 'package:device_info_plus/device_info_plus.dart';
export 'package:file_picker/file_picker.dart';
export 'package:flutter/foundation.dart';
export 'package:flutter/gestures.dart';
export 'package:flutter/material.dart';
export 'package:flutter/services.dart';
export 'package:flutter_svg/flutter_svg.dart';
export 'package:get/get.dart'
    hide Response, FormData, MultipartFile, HeaderValue;
export 'package:get_it/get_it.dart';
export 'package:google_fonts/google_fonts.dart';
export 'package:go_router/go_router.dart';
export 'package:http/http.dart';
export 'package:image_picker/image_picker.dart';
export 'package:intl/intl.dart' hide TextDirection;
export 'package:logger/logger.dart';
export 'package:lottie/lottie.dart';
export 'package:path_provider/path_provider.dart';
export 'package:permission_handler/permission_handler.dart';
export 'package:shimmer/shimmer.dart';
export 'package:url_launcher/url_launcher.dart';
export 'package:shared_preferences/shared_preferences.dart';
export 'package:youtube_player_iframe/youtube_player_iframe.dart';
export 'package:share_plus/share_plus.dart';

// :::::::::::::::::::::::::::::::::::::::::::::: app imports ::::::::::::::::::::::::::::::::::::::::::://
export 'app/cache/app_shared_preference.dart';
export 'app/config/app_config.dart';
export 'app/config/environment_config.dart';
export 'app/config/app_theme.dart';
export 'app/constants/app_constants.dart';
export 'app/extensions/height_sliver_delegate.dart';
export 'app/helper/ads_manager.dart';
export 'app/helper/app_helper.dart';
export 'app/helper/connectivity_helper.dart';
export 'app/helper/date_helper.dart';
export 'app/helper/error_handler.dart';
export 'app/helper/file_picker_helper.dart';
export 'app/helper/hive_helper.dart';
export 'app/helper/loading_manager.dart';
// export 'app/helper/mock_data_helper.dart'; // Disabled for now
export 'app/helper/offline_manager.dart';
export 'app/helper/permission_helper.dart';
export 'app/helper/responsive_helper.dart';
export 'app/helper/rest_helper.dart';
export 'app/helper/scroll_helper.dart';
export 'app/helper/validation_helper.dart';
export 'app/routes/app_routes.dart';
export 'app/routes/route_helper.dart';
export 'app/routes/app_navigation.dart';
export 'app/ui/app_buttons.dart';
export 'app/ui/app_loader.dart';
export 'app/ui/app_scaffold.dart';
export 'app/ui/typography.dart';
export 'app/ui/app_textform_field.dart';
export 'app/ui/app_toast.dart';
export 'app/ui/common_appbar.dart';
export 'app/ui/movie_card.dart';

// :::::::::::::::::::::::::::::::::::::::::::::: controller imports ::::::::::::::::::::::::::::::::::::::::::://
export 'controllers/authentication_controller.dart';
export 'controllers/main_navigation_controller.dart';
export 'controllers/home_controller.dart';
export 'controllers/video_player_controller.dart';
export 'controllers/playlist_controller.dart';
export 'controllers/add_movie_controller.dart';
export 'controllers/subscription_controller.dart';
export 'controllers/settings_controller.dart';
export 'controllers/search_controller.dart';

// :::::::::::::::::::::::::::::::::::::::::::::: enum imports ::::::::::::::::::::::::::::::::::::::::::://
export 'enum/api_status.dart';
export 'enum/user_type.dart';

// :::::::::::::::::::::::::::::::::::::::::::::: localization imports ::::::::::::::::::::::::::::::::::::::::::://
export 'main.dart';

// :::::::::::::::::::::::::::::::::::::::::::::: Model ::::::::::::::::::::::::::::::::::::::::::://
export 'model/user/user_model.dart';
export 'model/common/api_response_model.dart';
export 'model/movie/movie_model.dart';
export 'model/movie/category_model.dart';
export 'model/movie/playlist_model.dart';
export 'model/movie/movie_response_model.dart';
export 'model/movie/episode_model.dart';
export 'model/movie/category_wise_response.dart';
export 'model/filter/filter_model.dart';

// :::::::::::::::::::::::::::::::::::::::::::::: page imports ::::::::::::::::::::::::::::::::::::::::::://
export 'pages/authentication/login/login_page.dart';
export 'pages/authentication/login/login_page_helper.dart';
export 'pages/authentication/signup/signup_page.dart';
export 'pages/authentication/signup/signup_page_helper.dart';
export 'pages/authentication/forgot_password/forgot_password_page.dart';
export 'pages/authentication/forgot_password/forgot_password_page_helper.dart';
export 'pages/splash/splash_page.dart';
export 'pages/no_internet/no_internet_connection_page.dart';
export 'pages/main_navigation/main_navigation_page.dart';
export 'pages/video_player/video_player_page.dart';
export 'pages/search/search_page.dart';
export 'pages/home/<USER>';
export 'pages/home/<USER>';
export 'pages/video_player/video_player_page_helper.dart';
export 'pages/playlist/my_playlist_page.dart';
export 'pages/playlist/playlist_page_helper.dart';
export 'pages/subscription/subscription_page.dart';
export 'pages/subscription/subscription_page_helper.dart';
export 'pages/setting/settings_page.dart';
export 'pages/setting/settings_page_helper.dart';
export 'pages/setting/terms_of_service_page.dart';
export 'pages/setting/privacy_policy_page.dart';
export 'pages/setting/contact_support_page.dart';
export 'pages/profile/profile_page.dart';

// :::::::::::::::::::::::::::::::::::::::::::::: Repository imports ::::::::::::::::::::::::::::::::::::::::::://
export 'repository/authentication/authentication_repository.dart';
export 'repository/authentication/authentication_repository_impl.dart';
export 'repository/movie/movie_repository.dart';
